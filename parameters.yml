Resources:
  # Lambda function to create SecureString parameter
  CreateOAuthParameterFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: create-oauth-parameter-${self:provider.stage}
      Runtime: python3.9
      Handler: index.handler
      Role: !GetAtt CreateOAuthParameterRole.Arn
      Timeout: 60
      Code:
        ZipFile: |
          import boto3
          import json
          import cfnresponse

          def handler(event, context):
              try:
                  ssm = boto3.client('ssm')

                  if event['RequestType'] == 'Create' or event['RequestType'] == 'Update':
                      parameter_name = event['ResourceProperties']['ParameterName']
                      parameter_value = event['ResourceProperties']['ParameterValue']
                      description = event['ResourceProperties']['Description']

                      # Create or update the SecureString parameter
                      ssm.put_parameter(
                          Name=parameter_name,
                          Value=parameter_value,
                          Type='SecureString',
                          Description=description,
                          Overwrite=True,
                          Tags=[
                              {'Key': 'ENVIRONMENT', 'Value': event['ResourceProperties']['Environment']},
                              {'Key': 'TEAM', 'Value': 'EIP Development Team'},
                              {'Key': 'PROJECT', 'Value': 'APPHERO'},
                              {'Key': 'PURPOSE', 'Value': 'OAuth Authentication'}
                          ]
                      )

                      cfnresponse.send(event, context, cfnresponse.SUCCESS, {
                          'ParameterName': parameter_name
                      })

                  elif event['RequestType'] == 'Delete':
                      parameter_name = event['ResourceProperties']['ParameterName']
                      try:
                          ssm.delete_parameter(Name=parameter_name)
                      except ssm.exceptions.ParameterNotFound:
                          pass  # Parameter already deleted

                      cfnresponse.send(event, context, cfnresponse.SUCCESS, {})

              except Exception as e:
                  print(f"Error: {str(e)}")
                  cfnresponse.send(event, context, cfnresponse.FAILED, {})

  # IAM Role for the Lambda function
  CreateOAuthParameterRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: create-oauth-parameter-role-${self:provider.stage}
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:PutParameter
                  - ssm:DeleteParameter
                  - ssm:AddTagsToResource
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/apphero/${self:provider.stage}/*'
              - Effect: Allow
                Action:
                  - kms:Encrypt
                  - kms:Decrypt
                  - kms:ReEncrypt*
                  - kms:GenerateDataKey*
                  - kms:DescribeKey
                Resource: '*'

  # Custom resource to create the OAuth parameter
  AppHeroOAuthCredentials:
    Type: AWS::CloudFormation::CustomResource
    Properties:
      ServiceToken: !GetAtt CreateOAuthParameterFunction.Arn
      ParameterName: /apphero/${self:provider.stage}/agent-oap-oauth-tokens
      ParameterValue: 'PLACEHOLDER_VALUE_UPDATE_AFTER_DEPLOYMENT'
      Description: 'OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth'
      Environment: ${file(env.${self:provider.stage}.yml):variables.ENVIRONMENT_TAG}
