# Manual Steps Required After Deployment

## OAuth Parameter Store Setup

The CloudFormation stack automatically creates the OAuth credentials parameter in AWS Parameter Store with a placeholder value. After deployment, you need to update it with the actual credentials.

### For Development Environment

```bash
aws ssm put-parameter \
  --name "/apphero/dev/agent-oap-oauth-tokens" \
  --value "Basic <your-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1 \
  --overwrite \
  --description "OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth"
```

### For Production Environment

```bash
aws ssm put-parameter \
  --name "/apphero/prod/agent-oap-oauth-tokens" \
  --value "Basic <your-base64-encoded-credentials>" \
  --type "SecureString" \
  --region eu-west-1 \
  --overwrite \
  --description "OAuth credentials for authentication - stores Base64 encoded client_id:client_secret for Basic auth"
```

## Credential Format

The parameter value must be in the format:

```
Basic <base64-encoded-clientId:clientSecret>
```

### Example:

```bash
# If your credentials are:
# Client ID: your_client_id
# Client Secret: your_client_secret

# Create Base64 string:
echo -n "your_client_id:your_client_secret" | base64
# Result: eW91cl9jbGllbnRfaWQ6eW91cl9jbGllbnRfc2VjcmV0

# Parameter Store value:
Basic eW91cl9jbGllbnRfaWQ6eW91cl9jbGllbnRfc2VjcmV0
```

## Why Update After Deployment?

The OAuth credentials parameter is created via CloudFormation with a placeholder value, then updated manually because:

1. **Security**: Sensitive credentials should not be stored in CloudFormation templates
2. **Automation**: Infrastructure is created automatically via CloudFormation
3. **Flexibility**: Allows different credentials per environment without code changes
4. **Best Practice**: Separates infrastructure creation from secrets management

## Verification

After creating the parameter, you can test the OAuth functionality using the API endpoints documented in `OAUTH_API_DOCUMENTATION.md`.

## Environment Variables

The following environment variables are already configured and point to the correct parameter paths:

- **Dev**: `OAUTH_CREDENTIALS_PARAMETER: /apphero/dev/agent-oap-oauth-tokens`
- **Prod**: `OAUTH_CREDENTIALS_PARAMETER: /apphero/prod/agent-oap-oauth-tokens`
