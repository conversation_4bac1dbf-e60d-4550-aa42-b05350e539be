Resources:
  AppHeroRootAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/*/apphero/*'
  UnauthAppHeroRootAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/*/unauth/apphero/*'
  AppHeroCountryAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/GET/apphero/lookup/country'
  AppHeroUpdateConsentDetailsAPIGWInvokePermission:
    Type: 'AWS::Lambda::Permission'
    Properties:
      FunctionName: arn:aws:lambda:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:function:apphero-backend-service-${self:provider.stage}
      Action: 'lambda:InvokeFunction'
      Principal: 'apigateway.amazonaws.com'
      SourceArn: 'arn:aws:execute-api:${file(env.${self:provider.stage}.yml):variables.REGION}:${aws:accountId}:${file(env.${self:provider.stage}.yml):variables.APPHERO_API_ID}/*/PATCH/apphero/updateAppheroConsent'
  appSyncLogAccessRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Principal:
              Service:
                - 'appsync.amazonaws.com'
            Action:
              - 'sts:AssumeRole'
      Description: 'IAM role for AppSync'
      RoleName: appsync-logs-${sls:stage}
      Policies:
        - PolicyName: 'appsync-logs-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'logs:CreateLogStream'
                  - 'logs:CreateLogGroup'
                  - 'logs:PutLogEvents'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:*'
  appSyncDynamoDBServiceRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: 'Allow'
            Principal:
              Service:
                - 'appsync.amazonaws.com'
            Action:
              - 'sts:AssumeRole'
      Description: 'IAM role for AppSync to access dynamodb tables'
      RoleName: appSyncDynamoDBServiceRole-${sls:stage}
      Policies:
        - PolicyName: 'appSyncDynamoDBServicePolicy-${sls:stage}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: 'Allow'
                Action:
                  - 'dynamodb:*'
                Resource:
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*'
  # Note: apphero-lambda-exec-role-dev already exists outside CloudFormation
  # It is referenced in env.dev.yml and used by the Lambda function
  # Removed CloudFormation resource definition to avoid conflicts
